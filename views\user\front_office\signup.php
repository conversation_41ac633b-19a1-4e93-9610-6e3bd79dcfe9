<?php

require_once '../../../config.php';
require_once '../../../controllers/UserController.php';

// Create a new instance of UserController
$userC = new UserController();

if (
    isset($_POST["first_name"]) &&
    isset($_POST["last_name"]) &&
    isset($_POST["email"]) &&
    isset($_POST["password"])
) {
    if (
        !empty($_POST['first_name']) &&
        !empty($_POST['last_name']) &&
        !empty($_POST["email"]) &&
        !empty($_POST["password"])
    ) {
        // Server-side validation
        $firstName = trim($_POST['first_name']);
        $lastName = trim($_POST['last_name']);
        $email = trim($_POST['email']);
        $password = $_POST['password'];

        $errors = [];

        // Validate first name
        if (strlen($firstName) > 20) {
            $errors[] = "First name must not exceed 20 characters";
        }
        if (!preg_match('/^[A-Za-z\s]+$/', $firstName)) {
            $errors[] = "First name must contain only alphabetic characters";
        }

        // Validate last name
        if (strlen($lastName) > 20) {
            $errors[] = "Last name must not exceed 20 characters";
        }
        if (!preg_match('/^[A-Za-z\s]+$/', $lastName)) {
            $errors[] = "Last name must contain only alphabetic characters";
        }

        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Please enter a valid email format";
        }

        // Validate password
        if (strlen($password) < 6) {
            $errors[] = "Password must be at least 6 characters long";
        }

        if (!empty($errors)) {
            $error_message = implode('. ', $errors);
            header('Location: register.php?error_global=' . urlencode($error_message));
            exit();
        }

        // Initialize and set user properties
        $user = new User();
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setEmail($email);
        $user->setPassword($password);
        $user->setRole('client');
        $user->setVerified(0);
        $user->setBanned(0);
        $user->setAccountType('normal');
        
        try {
            // Attempt to create the user using UserController
            $userC->addUser($user);
            // Fetch the user (assuming getUserByEmail exists)
            $newUser = $userC->getUserByEmail($user->getEmail());
            if (session_status() === PHP_SESSION_NONE) {
                session_set_cookie_params(0, '/', '', true, true);
                session_start();
            }
            $_SESSION['user_id'] = $newUser['id'];
            $_SESSION['first_name'] = $newUser['first_name'];
            $_SESSION['last_name'] = $newUser['last_name'];
            $_SESSION['email'] = $newUser['email'];
            $_SESSION['role'] = $newUser['role'];
            $_SESSION['verified'] = $newUser['verified'];
            $_SESSION['banned'] = $newUser['banned'];
            $_SESSION['account_type'] = $newUser['account_type'];
            header('Location: home.php');
            exit();
        } catch (Exception $e) {
            $error_message = "Failed to create account. Please try again later.";
            header('Location: register.php?error_global=' . urlencode($error_message));
            exit();
        }
    } else {
        $error_message = "All fields are required.";
        header('Location: register.php?error_global=' . urlencode($error_message));
        exit();
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $recaptchaSecret = '6LfoESorAAAAAA95d8vIESOrFaW4e4dVsNBohfiX';
    $recaptchaResponse = $_POST['g-recaptcha-response'] ?? '';
    $recaptchaUrl = 'https://www.google.com/recaptcha/api/siteverify';
    $recaptcha = file_get_contents($recaptchaUrl . '?secret=' . $recaptchaSecret . '&response=' . $recaptchaResponse);
    $recaptcha = json_decode($recaptcha);
    if (!$recaptcha || !$recaptcha->success) {
        $error_message = 'Please complete the reCAPTCHA.';
        header('Location: register.php?error_global=' . urlencode($error_message));
        exit();
    }
}

?>