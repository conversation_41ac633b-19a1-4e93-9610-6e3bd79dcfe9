<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'agent') {
    header('Location: ../front_office/login.php');
    exit();
}

require_once '../../../controllers/SurveyController.php';

$surveyController = new SurveyController();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
            case 'update':
                $result = $surveyController->processSurveyForm($_POST);
                $message = $result['message'];
                $messageType = $result['status'] ? 'success' : 'danger';
                break;
                
            case 'delete':
                if (isset($_POST['survey_id'])) {
                    $result = $surveyController->deleteSurvey($_POST['survey_id']);
                    $message = $result['message'];
                    $messageType = $result['status'] ? 'success' : 'danger';
                }
                break;
        }
    }
}

// Get all surveys
$surveys = $surveyController->getAllSurveys();

// Get survey for editing if requested
$editSurvey = null;
$editQuestions = [];
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $editSurvey = $surveyController->getSurvey($_GET['edit']);
    if ($editSurvey) {
        $editQuestions = $surveyController->getQuestionsBySurvey($_GET['edit']);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Survey Management - Hotelia Smart Admin Dashboard</title>
    <meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
    <link rel="icon" href="template/assets/logo/HS.png" type="image/png" />
    <script src="template/assets/js/plugin/webfont/webfont.min.js"></script>
    <script>
      WebFont.load({
        google: { families: ["Public Sans:300,400,500,600,700"] },
        custom: {
          families: [
            "Font Awesome 5 Solid",
            "Font Awesome 5 Regular",
            "Font Awesome 5 Brands",
            "simple-line-icons",
          ],
          urls: ["template/assets/css/fonts.min.css"],
        },
        active: function () {
          sessionStorage.fonts = true;
        },
      });
    </script>
    <link rel="stylesheet" href="template/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="template/assets/css/plugins.min.css" />
    <link rel="stylesheet" href="template/assets/css/hotelia smart.min.css" />
    <link rel="stylesheet" href="template/assets/css/demo.css" />
    <style>
        .question-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .question-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        .question-type-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .add-question-btn {
            border: 2px dashed #007bff;
            background: transparent;
            color: #007bff;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .add-question-btn:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'dashboard_side_bar.php'; ?>
        <div class="main-panel">
            <?php include 'header_bar.php'; ?>
            <div class="content">
                <div class="page-inner">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex align-items-center">
                                        <h4 class="card-title">Survey Management</h4>
                                        <button class="btn btn-primary btn-round ms-auto" onclick="showCreateForm()">
                                            <i class="fa fa-plus"></i> Create New Survey
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <?php if ($message): ?>
                                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                            <?php echo htmlspecialchars($message); ?>
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Survey List -->
                                    <div id="survey-list">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Description</th>
                                                        <th>Questions</th>
                                                        <th>Assignments</th>
                                                        <th>Status</th>
                                                        <th>Created</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if (empty($surveys)): ?>
                                                        <tr>
                                                            <td colspan="7" class="text-center text-muted">No surveys found. Create your first survey!</td>
                                                        </tr>
                                                    <?php else: ?>
                                                        <?php foreach ($surveys as $survey): ?>
                                                            <tr>
                                                                <td><strong><?php echo htmlspecialchars($survey['title']); ?></strong></td>
                                                                <td><?php echo htmlspecialchars(substr($survey['description'] ?? '', 0, 50)) . (strlen($survey['description'] ?? '') > 50 ? '...' : ''); ?></td>
                                                                <td><span class="badge badge-info"><?php echo $survey['question_count']; ?> questions</span></td>
                                                                <td><span class="badge badge-secondary"><?php echo $survey['assignment_count']; ?> assignments</span></td>
                                                                <td>
                                                                    <span class="badge badge-<?php echo $survey['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                                        <?php echo ucfirst($survey['status']); ?>
                                                                    </span>
                                                                </td>
                                                                <td><?php echo date('Y-m-d', strtotime($survey['created_at'])); ?></td>
                                                                <td>
                                                                    <div class="btn-group" role="group">
                                                                        <a href="?edit=<?php echo $survey['survey_id']; ?>" class="btn btn-sm btn-info">
                                                                            <i class="fa fa-edit"></i>
                                                                        </a>
                                                                        <button class="btn btn-sm btn-danger" onclick="deleteSurvey(<?php echo $survey['survey_id']; ?>, '<?php echo htmlspecialchars($survey['title']); ?>')">
                                                                            <i class="fa fa-trash"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Survey Form (Create/Edit) -->
                                    <div id="survey-form" style="display: <?php echo $editSurvey ? 'block' : 'none'; ?>;">
                                        <hr>
                                        <h5><?php echo $editSurvey ? 'Edit Survey' : 'Create New Survey'; ?></h5>
                                        <form method="POST" action="">
                                            <input type="hidden" name="action" value="<?php echo $editSurvey ? 'update' : 'create'; ?>">
                                            <?php if ($editSurvey): ?>
                                                <input type="hidden" name="survey_id" value="<?php echo $editSurvey['survey_id']; ?>">
                                            <?php endif; ?>
                                            
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        <label for="title">Survey Title *</label>
                                                        <input type="text" class="form-control" id="title" name="title" 
                                                               value="<?php echo $editSurvey ? htmlspecialchars($editSurvey['title']) : ''; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="status">Status</label>
                                                        <select class="form-control" id="status" name="status">
                                                            <option value="active" <?php echo ($editSurvey && $editSurvey['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                                            <option value="inactive" <?php echo ($editSurvey && $editSurvey['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                                            <option value="draft" <?php echo ($editSurvey && $editSurvey['status'] === 'draft') ? 'selected' : ''; ?>>Draft</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="description">Description</label>
                                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo $editSurvey ? htmlspecialchars($editSurvey['description']) : ''; ?></textarea>
                                            </div>

                                            <!-- Questions Section -->
                                            <div class="form-group">
                                                <label>Questions</label>
                                                <div id="questions-container">
                                                    <?php if ($editQuestions): ?>
                                                        <?php foreach ($editQuestions as $index => $question): ?>
                                                            <div class="question-item" data-question-index="<?php echo $index; ?>">
                                                                <div class="question-header">
                                                                    <span class="question-type-badge badge badge-primary"><?php echo ucfirst($question['question_type']); ?></span>
                                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">
                                                                        <i class="fa fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                                <div class="form-group">
                                                                    <input type="text" class="form-control" name="questions[<?php echo $index; ?>][text]" 
                                                                           placeholder="Question text" value="<?php echo htmlspecialchars($question['question_text']); ?>" required>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <select class="form-control question-type" name="questions[<?php echo $index; ?>][type]" onchange="toggleQuestionOptions(this)">
                                                                            <option value="text" <?php echo $question['question_type'] === 'text' ? 'selected' : ''; ?>>Text</option>
                                                                            <option value="multiple_choice" <?php echo $question['question_type'] === 'multiple_choice' ? 'selected' : ''; ?>>Multiple Choice</option>
                                                                            <option value="rating" <?php echo $question['question_type'] === 'rating' ? 'selected' : ''; ?>>Rating (1-5)</option>
                                                                            <option value="yes_no" <?php echo $question['question_type'] === 'yes_no' ? 'selected' : ''; ?>>Yes/No</option>
                                                                        </select>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-check">
                                                                            <input type="checkbox" class="form-check-input" name="questions[<?php echo $index; ?>][required]" 
                                                                                   <?php echo $question['is_required'] ? 'checked' : ''; ?>>
                                                                            <label class="form-check-label">Required</label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="add-question-btn" onclick="addQuestion()">
                                                    <i class="fa fa-plus"></i> Add Question
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fa fa-save"></i> <?php echo $editSurvey ? 'Update Survey' : 'Create Survey'; ?>
                                                </button>
                                                <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                                                    <i class="fa fa-times"></i> Cancel
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the survey "<span id="survey-title"></span>"?</p>
                    <p class="text-danger"><small>This will also delete all questions and responses associated with this survey.</small></p>
                </div>
                <div class="modal-footer">
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="survey_id" id="delete-survey-id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Survey</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="template/assets/js/core/jquery-3.7.1.min.js"></script>
    <script src="template/assets/js/core/popper.min.js"></script>
    <script src="template/assets/js/core/bootstrap.min.js"></script>
    <script src="template/assets/js/plugins/perfect-scrollbar.jquery.min.js"></script>
    <script src="template/assets/js/plugins/moment.min.js"></script>
    <script src="template/assets/js/plugins/sweetalert2.min.js"></script>
    <script src="template/assets/js/hotelia smart.min.js"></script>
    <script src="template/assets/js/setting-demo2.js"></script>

    <script>
        let questionIndex = <?php echo count($editQuestions); ?>;

        function showCreateForm() {
            document.getElementById('survey-form').style.display = 'block';
            document.getElementById('survey-list').style.display = 'none';
        }

        function cancelForm() {
            window.location.href = 'surveys.php';
        }

        function addQuestion() {
            const container = document.getElementById('questions-container');
            const questionHtml = `
                <div class="question-item" data-question-index="${questionIndex}">
                    <div class="question-header">
                        <span class="question-type-badge badge badge-primary">Text</span>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" name="questions[${questionIndex}][text]"
                               placeholder="Question text" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <select class="form-control question-type" name="questions[${questionIndex}][type]" onchange="toggleQuestionOptions(this)">
                                <option value="text">Text</option>
                                <option value="multiple_choice">Multiple Choice</option>
                                <option value="rating">Rating (1-5)</option>
                                <option value="yes_no">Yes/No</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="questions[${questionIndex}][required]">
                                <label class="form-check-label">Required</label>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', questionHtml);
            questionIndex++;
        }

        function removeQuestion(button) {
            button.closest('.question-item').remove();
        }

        function toggleQuestionOptions(select) {
            const badge = select.closest('.question-item').querySelector('.question-type-badge');
            badge.textContent = select.options[select.selectedIndex].text;
        }

        function deleteSurvey(surveyId, surveyTitle) {
            document.getElementById('survey-title').textContent = surveyTitle;
            document.getElementById('delete-survey-id').value = surveyId;

            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // Initialize question type badges on page load
        document.addEventListener('DOMContentLoaded', function() {
            const questionTypes = document.querySelectorAll('.question-type');
            questionTypes.forEach(select => {
                const badge = select.closest('.question-item').querySelector('.question-type-badge');
                badge.textContent = select.options[select.selectedIndex].text;
            });
        });
    </script>
