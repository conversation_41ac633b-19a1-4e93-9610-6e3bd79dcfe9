-- Survey System Tables
-- This file contains the SQL structure for the survey system

-- Table for storing surveys
CREATE TABLE IF NOT EXISTS surveys (
    survey_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    FOREIGN KEY (created_by) REFERENCES user(user_id) ON DELETE CASCADE
);

-- Table for storing survey questions
CREATE TABLE IF NOT EXISTS survey_questions (
    question_id INT AUTO_INCREMENT PRIMARY KEY,
    survey_id INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('text', 'multiple_choice', 'rating', 'yes_no') DEFAULT 'text',
    options JSON NULL, -- For multiple choice options
    is_required BOOLEAN DEFAULT FALSE,
    question_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (survey_id) REFERENCES surveys(survey_id) ON DELETE CASCADE
);

-- Table for tracking survey assignments to clients
CREATE TABLE IF NOT EXISTS survey_assignments (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    survey_id INT NOT NULL,
    client_id INT NOT NULL,
    assigned_by INT NOT NULL, -- Agent who assigned the survey
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'completed', 'expired') DEFAULT 'pending',
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (survey_id) REFERENCES surveys(survey_id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES user(user_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES user(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_assignment (survey_id, client_id)
);

-- Table for storing client responses to surveys
CREATE TABLE IF NOT EXISTS survey_responses (
    response_id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    question_id INT NOT NULL,
    response_text TEXT,
    response_value INT NULL, -- For rating questions
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (assignment_id) REFERENCES survey_assignments(assignment_id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES survey_questions(question_id) ON DELETE CASCADE,
    UNIQUE KEY unique_response (assignment_id, question_id)
);

-- Insert a default sample survey for testing
INSERT INTO surveys (title, description, created_by, status) VALUES 
('Customer Satisfaction Survey', 'Please help us improve our services by answering these questions.', 1, 'active');

-- Get the survey ID for the sample survey
SET @survey_id = LAST_INSERT_ID();

-- Insert sample questions for the default survey
INSERT INTO survey_questions (survey_id, question_text, question_type, is_required, question_order) VALUES
(@survey_id, 'How satisfied are you with our service?', 'rating', TRUE, 1),
(@survey_id, 'What can we improve?', 'text', FALSE, 2),
(@survey_id, 'Would you recommend us to others?', 'yes_no', TRUE, 3);
