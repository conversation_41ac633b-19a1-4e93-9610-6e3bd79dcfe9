/**
 * User Registration Form Validation
 * Provides client-side validation for user registration forms
 */

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[action="signup.php"]');
    if (!form) return;

    const firstNameInput = form.querySelector('input[name="first_name"]');
    const lastNameInput = form.querySelector('input[name="last_name"]');
    const emailInput = form.querySelector('input[name="email"]');
    const passwordInput = form.querySelector('input[name="password"]');
    const submitButton = form.querySelector('button[type="submit"]');

    // Create error message containers
    function createErrorContainer(input) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            color: #d93025;
            font-size: 14px;
            margin-top: 4px;
            margin-bottom: 8px;
            display: none;
            font-weight: 500;
        `;
        input.parentNode.appendChild(errorDiv);
        return errorDiv;
    }

    // Get or create error containers
    const firstNameError = createErrorContainer(firstNameInput);
    const lastNameError = createErrorContainer(lastNameInput);
    const emailError = createErrorContainer(emailInput);
    const passwordError = createErrorContainer(passwordInput);

    // Validation functions
    function validateName(name, fieldName) {
        const nameRegex = /^[A-Za-z\s]+$/;
        
        if (!name.trim()) {
            return `${fieldName} is required`;
        }
        
        if (name.length > 20) {
            return `${fieldName} must not exceed 20 characters`;
        }
        
        if (!nameRegex.test(name)) {
            return `${fieldName} must contain only alphabetic characters`;
        }
        
        return null;
    }

    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!email.trim()) {
            return 'Email is required';
        }
        
        if (!emailRegex.test(email)) {
            return 'Please enter a valid email format';
        }
        
        return null;
    }

    function validatePassword(password) {
        if (!password) {
            return 'Password is required';
        }
        
        if (password.length < 6) {
            return 'Password must be at least 6 characters long';
        }
        
        return null;
    }

    // Show/hide error messages
    function showError(errorElement, message) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        errorElement.parentNode.querySelector('input').style.borderColor = '#d93025';
    }

    function hideError(errorElement) {
        errorElement.style.display = 'none';
        errorElement.parentNode.querySelector('input').style.borderColor = '#e0e7ef';
    }

    // Real-time validation
    firstNameInput.addEventListener('input', function() {
        const error = validateName(this.value, 'First name');
        if (error) {
            showError(firstNameError, error);
        } else {
            hideError(firstNameError);
        }
    });

    firstNameInput.addEventListener('blur', function() {
        const error = validateName(this.value, 'First name');
        if (error) {
            showError(firstNameError, error);
        } else {
            hideError(firstNameError);
        }
    });

    lastNameInput.addEventListener('input', function() {
        const error = validateName(this.value, 'Last name');
        if (error) {
            showError(lastNameError, error);
        } else {
            hideError(lastNameError);
        }
    });

    lastNameInput.addEventListener('blur', function() {
        const error = validateName(this.value, 'Last name');
        if (error) {
            showError(lastNameError, error);
        } else {
            hideError(lastNameError);
        }
    });

    emailInput.addEventListener('input', function() {
        const error = validateEmail(this.value);
        if (error) {
            showError(emailError, error);
        } else {
            hideError(emailError);
        }
    });

    emailInput.addEventListener('blur', function() {
        const error = validateEmail(this.value);
        if (error) {
            showError(emailError, error);
        } else {
            hideError(emailError);
        }
    });

    passwordInput.addEventListener('input', function() {
        const error = validatePassword(this.value);
        if (error) {
            showError(passwordError, error);
        } else {
            hideError(passwordError);
        }
    });

    passwordInput.addEventListener('blur', function() {
        const error = validatePassword(this.value);
        if (error) {
            showError(passwordError, error);
        } else {
            hideError(passwordError);
        }
    });

    // Form submission validation
    form.addEventListener('submit', function(e) {
        let hasErrors = false;

        // Validate all fields
        const firstNameErr = validateName(firstNameInput.value, 'First name');
        const lastNameErr = validateName(lastNameInput.value, 'Last name');
        const emailErr = validateEmail(emailInput.value);
        const passwordErr = validatePassword(passwordInput.value);

        // Show errors if any
        if (firstNameErr) {
            showError(firstNameError, firstNameErr);
            hasErrors = true;
        } else {
            hideError(firstNameError);
        }

        if (lastNameErr) {
            showError(lastNameError, lastNameErr);
            hasErrors = true;
        } else {
            hideError(lastNameError);
        }

        if (emailErr) {
            showError(emailError, emailErr);
            hasErrors = true;
        } else {
            hideError(emailError);
        }

        if (passwordErr) {
            showError(passwordError, passwordErr);
            hasErrors = true;
        } else {
            hideError(passwordError);
        }

        // Prevent form submission if there are errors
        if (hasErrors) {
            e.preventDefault();
            
            // Scroll to first error
            const firstError = form.querySelector('.error-message[style*="block"]');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            return false;
        }

        // Check reCAPTCHA if present
        const recaptcha = form.querySelector('.g-recaptcha');
        if (recaptcha) {
            const recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                alert('Please complete the reCAPTCHA verification.');
                e.preventDefault();
                return false;
            }
        }
    });

    // Remove HTML5 validation attributes to rely on JavaScript validation
    firstNameInput.removeAttribute('required');
    lastNameInput.removeAttribute('required');
    emailInput.removeAttribute('required');
    passwordInput.removeAttribute('required');
});
