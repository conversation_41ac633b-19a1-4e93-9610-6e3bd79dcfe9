<?php

class Rating {
    private $ratingId;
    private $auteur_id;
    private $userId;
    private $ratingValue;

    public function __construct($auteur_id, $userId, $ratingValue) {
        $this->auteur_id = $auteur_id;
        $this->userId = $userId;
        $this->ratingValue = $ratingValue;
    }

    public function getRatingId() {
        return $this->ratingId;
    }

    public function getArticleId() {
        return $this->auteur_id;
    }

    public function getUserId() {
        return $this->userId;
    }

    public function getRatingValue() {
        return $this->ratingValue;
    }

    // Save or update the user's rating for an article
    public function saveOrUpdate($conn) {
        $query = "SELECT * FROM ratings WHERE auteur_id = ? AND user_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$this->auteur_id, $this->userId]);
        $result = $stmt->fetchAll();
        if ($result && count($result) > 0) {
            // Update existing rating
            $update = "UPDATE ratings SET rating_value = ? WHERE auteur_id = ? AND user_id = ?";
            $stmt = $conn->prepare($update);
            return $stmt->execute([$this->ratingValue, $this->auteur_id, $this->userId]);
        } else {
            // Insert new rating
            $insert = "INSERT INTO ratings (auteur_id, user_id, rating_value) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insert);
            return $stmt->execute([$this->auteur_id, $this->userId, $this->ratingValue]);
        }
    }

    // Get average rating for an article
    public static function getAverageRating($conn, $auteur_id) {
        $query = "SELECT AVG(rating_value) as avg_rating FROM ratings WHERE auteur_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$auteur_id]);
        $row = $stmt->fetch();
        if ($row && isset($row['avg_rating'])) {
            return round($row['avg_rating'], 2);
        }
        return null;
    }

    // Get a user's rating for an article
    public static function getUserRating($conn, $auteur_id, $userId) {
        $query = "SELECT rating_value FROM ratings WHERE auteur_id = ? AND user_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$auteur_id, $userId]);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return $row['rating_value'];
        }
        return null;
    }
}