<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'client') {
    header('Location: ../front_office/login.php');
    exit();
}

require_once '../../../../config.php';

// Get PDO connection
$pdo = config::getConnexion();

// Handle survey response submission
$response_submitted = false;
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assignment_id'])) {
    $assignment_id = intval($_POST['assignment_id']);
    $user_id = $_SESSION['user_id'];

    try {
        // Verify this assignment belongs to the current user
        $verify_sql = "SELECT sa.assignment_id, sa.survey_id FROM survey_assignments sa WHERE sa.assignment_id = ? AND sa.client_id = ? AND sa.status = 'pending'";
        $verify_stmt = $pdo->prepare($verify_sql);
        $verify_stmt->execute([$assignment_id, $user_id]);
        $assignment = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        if ($assignment) {
            $pdo->beginTransaction();

            // Delete existing responses for this assignment
            $delete_sql = "DELETE FROM survey_responses WHERE assignment_id = ?";
            $delete_stmt = $pdo->prepare($delete_sql);
            $delete_stmt->execute([$assignment_id]);

            // Insert new responses
            $response_sql = "INSERT INTO survey_responses (assignment_id, question_id, response_text, response_value) VALUES (?, ?, ?, ?)";
            $response_stmt = $pdo->prepare($response_sql);

            foreach ($_POST as $key => $value) {
                if (strpos($key, 'question_') === 0) {
                    $question_id = intval(str_replace('question_', '', $key));
                    $response_text = is_array($value) ? implode(', ', $value) : trim($value);
                    $response_value = is_numeric($value) ? intval($value) : null;

                    if ($response_text !== '') {
                        $response_stmt->execute([$assignment_id, $question_id, $response_text, $response_value]);
                    }
                }
            }

            // Mark assignment as completed
            $complete_sql = "UPDATE survey_assignments SET status = 'completed', completed_at = NOW() WHERE assignment_id = ?";
            $complete_stmt = $pdo->prepare($complete_sql);
            $complete_stmt->execute([$assignment_id]);

            $pdo->commit();
            $response_submitted = true;
        } else {
            $error = 'Invalid survey assignment.';
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = 'Failed to submit survey response: ' . $e->getMessage();
        error_log("Error submitting survey response: " . $e->getMessage());
    }
}

// Fetch assigned surveys for the user
$user_id = $_SESSION['user_id'];
$surveys = [];
try {
    $surveys_sql = "SELECT sa.assignment_id, sa.assigned_at, sa.status, s.title, s.description,
                           u.first_name AS assigned_by_first, u.last_name AS assigned_by_last
                    FROM survey_assignments sa
                    JOIN surveys s ON sa.survey_id = s.survey_id
                    JOIN user u ON sa.assigned_by = u.user_id
                    WHERE sa.client_id = ?
                    ORDER BY sa.assigned_at DESC";
    $surveys_stmt = $pdo->prepare($surveys_sql);
    $surveys_stmt->execute([$user_id]);
    $surveys = $surveys_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error fetching surveys: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survey Center</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/stylefront3.css">
    <style>
        .survey-container { max-width: 800px; margin: 40px auto; background: #fff; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); padding: 32px; }
        .survey-item { border: 1px solid #e0e7ef; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .survey-item.pending { border-left: 4px solid #ffc107; }
        .survey-item.completed { border-left: 4px solid #28a745; }
        .survey-meta { font-size: 13px; color: #888; margin-bottom: 10px; }
        .survey-title { font-size: 18px; font-weight: 600; color: #2d3e50; margin-bottom: 8px; }
        .survey-description { color: #666; margin-bottom: 15px; }
        .survey-status { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .question-group { margin-bottom: 20px; }
        .question-text { font-weight: 600; margin-bottom: 10px; }
        .rating-group { display: flex; gap: 10px; }
        .rating-option { padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }
        .rating-option:hover, .rating-option.selected { background: #007bff; color: white; }
    </style>
</head>
<body style="background: #f5f7fa;">
    <div class="survey-container">
        <h2 style="text-align:center; font-weight:700; color:#2d3e50; margin-bottom:24px;">Survey Center</h2>
        <?php if ($response_submitted): ?>
            <div class="alert alert-success">Survey response submitted successfully!</div>
        <?php elseif ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div style="margin-bottom: 20px;">
            <a href="../profile.php" class="btn btn-secondary">Back to Profile</a>
        </div>
        <h4 style="margin-top:32px;">Your Assigned Surveys</h4>
        <div class="survey-list">
            <?php if (empty($surveys)): ?>
                <div style="color:#888; text-align:center; padding: 40px;">No surveys assigned yet.</div>
            <?php else: ?>
                <?php foreach ($surveys as $survey): ?>
                    <div class="survey-item <?php echo $survey['status']; ?>">
                        <div class="survey-meta">
                            Assigned by: <strong><?php echo htmlspecialchars($survey['assigned_by_first'] . ' ' . $survey['assigned_by_last']); ?></strong>
                            <span style="float:right; color:#aaa;">
                                <?php echo date('Y-m-d H:i', strtotime($survey['assigned_at'])); ?>
                            </span>
                            <span class="survey-status status-<?php echo $survey['status']; ?>" style="float:right; margin-right: 10px;">
                                <?php echo ucfirst($survey['status']); ?>
                            </span>
                        </div>
                        <div class="survey-title"><?php echo htmlspecialchars($survey['title']); ?></div>
                        <div class="survey-description"><?php echo htmlspecialchars($survey['description']); ?></div>

                        <?php if ($survey['status'] === 'pending'): ?>
                            <button class="btn btn-primary btn-sm" onclick="showSurveyForm(<?php echo $survey['assignment_id']; ?>)">
                                Take Survey
                            </button>
                        <?php else: ?>
                            <span class="text-success">✓ Completed</span>
                        <?php endif; ?>

                        <!-- Survey Form (hidden by default) -->
                        <div id="survey-form-<?php echo $survey['assignment_id']; ?>" style="display: none; margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
                            <?php
                            // Fetch questions for this survey
                            try {
                                $questions_sql = "SELECT sq.* FROM survey_questions sq
                                                 JOIN survey_assignments sa ON sq.survey_id = sa.survey_id
                                                 WHERE sa.assignment_id = ?
                                                 ORDER BY sq.question_order";
                                $questions_stmt = $pdo->prepare($questions_sql);
                                $questions_stmt->execute([$survey['assignment_id']]);
                                $questions = $questions_stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (Exception $e) {
                                $questions = [];
                                error_log("Error fetching questions: " . $e->getMessage());
                            }
                            ?>

                            <form method="POST" action="">
                                <input type="hidden" name="assignment_id" value="<?php echo $survey['assignment_id']; ?>">

                                <?php foreach ($questions as $question): ?>
                                    <div class="question-group">
                                        <div class="question-text">
                                            <?php echo htmlspecialchars($question['question_text']); ?>
                                            <?php if ($question['is_required']): ?>
                                                <span style="color: red;">*</span>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($question['question_type'] === 'text'): ?>
                                            <textarea name="question_<?php echo $question['question_id']; ?>"
                                                    class="form-control" rows="3"
                                                    <?php echo $question['is_required'] ? 'required' : ''; ?>></textarea>

                                        <?php elseif ($question['question_type'] === 'rating'): ?>
                                            <div class="rating-group">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <label class="rating-option">
                                                        <input type="radio" name="question_<?php echo $question['question_id']; ?>"
                                                               value="<?php echo $i; ?>" style="display: none;"
                                                               <?php echo $question['is_required'] ? 'required' : ''; ?>>
                                                        <?php echo $i; ?>
                                                    </label>
                                                <?php endfor; ?>
                                            </div>

                                        <?php elseif ($question['question_type'] === 'yes_no'): ?>
                                            <div>
                                                <label style="margin-right: 20px;">
                                                    <input type="radio" name="question_<?php echo $question['question_id']; ?>"
                                                           value="Yes" <?php echo $question['is_required'] ? 'required' : ''; ?>>
                                                    Yes
                                                </label>
                                                <label>
                                                    <input type="radio" name="question_<?php echo $question['question_id']; ?>"
                                                           value="No" <?php echo $question['is_required'] ? 'required' : ''; ?>>
                                                    No
                                                </label>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>

                                <div style="margin-top: 20px;">
                                    <button type="submit" class="btn btn-success">Submit Survey</button>
                                    <button type="button" class="btn btn-secondary" onclick="hideSurveyForm(<?php echo $survey['assignment_id']; ?>)">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function showSurveyForm(assignmentId) {
            document.getElementById('survey-form-' + assignmentId).style.display = 'block';
        }

        function hideSurveyForm(assignmentId) {
            document.getElementById('survey-form-' + assignmentId).style.display = 'none';
        }

        // Rating system interaction
        document.addEventListener('DOMContentLoaded', function() {
            const ratingOptions = document.querySelectorAll('.rating-option');
            ratingOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');
                    const name = radio.name;

                    // Remove selected class from all options with same name
                    document.querySelectorAll(`input[name="${name}"]`).forEach(r => {
                        r.parentElement.classList.remove('selected');
                    });

                    // Add selected class to clicked option
                    this.classList.add('selected');
                    radio.checked = true;
                });
            });
        });
    </script>
</body>
</html>
