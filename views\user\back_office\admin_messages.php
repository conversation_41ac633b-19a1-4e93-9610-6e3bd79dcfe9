<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'agent') {
    header('Location: ../front_office/login.php');
    exit();
}

require_once '../../../config.php';

// Get PDO connection
$pdo = config::getConnexion();
// Handle survey assignment
$assignment_sent = false;
$assignment_error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['client_id'], $_POST['survey_id'])) {
    $client_id = intval($_POST['client_id']);
    $survey_id = intval($_POST['survey_id']);
    $agent_id = $_SESSION['user_id'];

    if ($client_id && $survey_id) {
        try {
            // Check if assignment already exists
            $check_sql = "SELECT assignment_id FROM survey_assignments WHERE survey_id = ? AND client_id = ?";
            $check_stmt = $pdo->prepare($check_sql);
            $check_stmt->execute([$survey_id, $client_id]);

            if ($check_stmt->fetch()) {
                $assignment_error = 'This survey has already been assigned to this client.';
            } else {
                // Insert new assignment
                $assign_sql = "INSERT INTO survey_assignments (survey_id, client_id, assigned_by) VALUES (?, ?, ?)";
                $assign_stmt = $pdo->prepare($assign_sql);
                if ($assign_stmt->execute([$survey_id, $client_id, $agent_id])) {
                    $assignment_sent = true;
                } else {
                    $assignment_error = 'Failed to assign survey.';
                }
            }
        } catch (Exception $e) {
            $assignment_error = 'Failed to assign survey: ' . $e->getMessage();
            error_log("Error assigning survey: " . $e->getMessage());
        }
    } else {
        $assignment_error = 'Please select both a client and a survey.';
    }
}
// Fetch clients for assignment
$clients = [];
try {
    $clients_sql = "SELECT user_id, first_name, last_name, email FROM user WHERE role = 'client' ORDER BY first_name, last_name";
    $clients_stmt = $pdo->prepare($clients_sql);
    $clients_stmt->execute();
    $clients = $clients_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error fetching clients: " . $e->getMessage());
}

// Fetch available surveys using SurveyController
require_once '../../../controllers/SurveyController.php';
$surveyController = new SurveyController();
$surveys = $surveyController->getActiveSurveys();

// Fetch survey assignments and responses
$assignments = [];
try {
    $assignments_sql = "SELECT sa.assignment_id, sa.assigned_at, sa.status, sa.completed_at,
                               s.title as survey_title,
                               u.first_name, u.last_name, u.email
                        FROM survey_assignments sa
                        JOIN surveys s ON sa.survey_id = s.survey_id
                        JOIN user u ON sa.client_id = u.user_id
                        WHERE sa.assigned_by = ?
                        ORDER BY sa.assigned_at DESC";
    $assignments_stmt = $pdo->prepare($assignments_sql);
    $assignments_stmt->execute([$_SESSION['user_id']]);
    $assignments = $assignments_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error fetching assignments: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Survey Management - Hotelia Smart Admin Dashboard</title>
    <meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
    <link rel="icon" href="template/assets/logo/HS.png" type="image/png" />
    <script src="template/assets/js/plugin/webfont/webfont.min.js"></script>
    <script>
      WebFont.load({
        google: { families: ["Public Sans:300,400,500,600,700"] },
        custom: {
          families: [
            "Font Awesome 5 Solid",
            "Font Awesome 5 Regular",
            "Font Awesome 5 Brands",
            "simple-line-icons",
          ],
          urls: ["template/assets/css/fonts.min.css"],
        },
        active: function () {
          sessionStorage.fonts = true;
        },
      });
    </script>
    <link rel="stylesheet" href="template/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="template/assets/css/plugins.min.css" />
    <link rel="stylesheet" href="template/assets/css/hotelia smart.min.css" />
    <link rel="stylesheet" href="template/assets/css/demo.css" />
</head>
<body>
    <div class="wrapper">
        <?php include 'dashboard_side_bar.php'; ?>
        <div class="main-panel">
            <?php include 'header_bar.php'; ?>
            <div class="content">
                <div class="page-inner">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Survey Assignment</h4>
                                    <a href="users_management.php" class="btn btn-secondary" style="margin-top:10px;">Back to Dashboard</a>
                                </div>
                                <div class="card-body">
                                    <?php if ($assignment_sent): ?>
                                        <div class="alert alert-success">Survey assigned successfully!</div>
                                    <?php elseif ($assignment_error): ?>
                                        <div class="alert alert-danger"><?php echo htmlspecialchars($assignment_error); ?></div>
                                    <?php endif; ?>

                                    <!-- Survey Assignment Form -->
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <h5>Assign Survey to Client</h5>
                                            <form method="POST" action="">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="client_id">Select Client:</label>
                                                            <select name="client_id" id="client_id" class="form-control" required>
                                                                <option value="">Choose Client</option>
                                                                <?php foreach ($clients as $client): ?>
                                                                    <option value="<?php echo $client['user_id']; ?>">
                                                                        <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name'] . ' (' . $client['email'] . ')'); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="survey_id">Select Survey:</label>
                                                            <select name="survey_id" id="survey_id" class="form-control" required>
                                                                <option value="">Choose Survey</option>
                                                                <?php foreach ($surveys as $survey): ?>
                                                                    <option value="<?php echo $survey['survey_id']; ?>">
                                                                        <?php echo htmlspecialchars($survey['title']); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>&nbsp;</label>
                                                            <button type="submit" class="btn btn-primary form-control">Assign Survey</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <!-- Survey Assignments Table -->
                                    <h5>Survey Assignments</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Client</th>
                                                    <th>Email</th>
                                                    <th>Survey</th>
                                                    <th>Assigned Date</th>
                                                    <th>Status</th>
                                                    <th>Completed Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if (empty($assignments)): ?>
                                                    <tr><td colspan="7" style="color:#888; text-align:center;">No survey assignments yet.</td></tr>
                                                <?php else: ?>
                                                    <?php foreach ($assignments as $assignment): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($assignment['first_name'] . ' ' . $assignment['last_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($assignment['email']); ?></td>
                                                            <td><?php echo htmlspecialchars($assignment['survey_title']); ?></td>
                                                            <td><?php echo date('Y-m-d H:i', strtotime($assignment['assigned_at'])); ?></td>
                                                            <td>
                                                                <span class="badge badge-<?php echo $assignment['status'] === 'completed' ? 'success' : ($assignment['status'] === 'pending' ? 'warning' : 'secondary'); ?>">
                                                                    <?php echo ucfirst($assignment['status']); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <?php echo $assignment['completed_at'] ? date('Y-m-d H:i', strtotime($assignment['completed_at'])) : '-'; ?>
                                                            </td>
                                                            <td>
                                                                <?php if ($assignment['status'] === 'completed'): ?>
                                                                    <button class="btn btn-info btn-sm" onclick="viewResponses(<?php echo $assignment['assignment_id']; ?>)">
                                                                        View Responses
                                                                    </button>
                                                                <?php else: ?>
                                                                    <span class="text-muted">Pending</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function viewResponses(assignmentId) {
            // This will be implemented later when you add the survey management system
            alert('Survey responses viewer will be implemented in the survey management system.');
        }
    </script>
</body>
</html>
