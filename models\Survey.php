<?php
require_once __DIR__ . '/../config.php';

class Survey {
    private $db;
    private $survey_id;
    private $title;
    private $description;
    private $created_by;
    private $created_at;
    private $updated_at;
    private $status;

    public function __construct() {
        $this->db = config::getConnexion();
    }

    // Getters
    public function getSurveyId() { return $this->survey_id; }
    public function getTitle() { return $this->title; }
    public function getDescription() { return $this->description; }
    public function getCreatedBy() { return $this->created_by; }
    public function getCreatedAt() { return $this->created_at; }
    public function getUpdatedAt() { return $this->updated_at; }
    public function getStatus() { return $this->status; }

    // Setters
    public function setSurveyId($survey_id) { $this->survey_id = $survey_id; }
    public function setTitle($title) { $this->title = $title; }
    public function setDescription($description) { $this->description = $description; }
    public function setCreatedBy($created_by) { $this->created_by = $created_by; }
    public function setCreatedAt($created_at) { $this->created_at = $created_at; }
    public function setUpdatedAt($updated_at) { $this->updated_at = $updated_at; }
    public function setStatus($status) { $this->status = $status; }

    // Create survey
    public function create() {
        try {
            $sql = "INSERT INTO surveys (title, description, created_by, status) VALUES (?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $this->title,
                $this->description,
                $this->created_by,
                $this->status ?? 'active'
            ]);
            
            if ($result) {
                $this->survey_id = $this->db->lastInsertId();
                return ['status' => true, 'message' => 'Survey created successfully', 'survey_id' => $this->survey_id];
            }
            return ['status' => false, 'message' => 'Failed to create survey'];
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error creating survey: ' . $e->getMessage()];
        }
    }

    // Read survey by ID
    public function read($survey_id) {
        try {
            $sql = "SELECT s.*, u.first_name, u.last_name 
                    FROM surveys s 
                    LEFT JOIN user u ON s.created_by = u.user_id 
                    WHERE s.survey_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$survey_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                $this->survey_id = $result['survey_id'];
                $this->title = $result['title'];
                $this->description = $result['description'];
                $this->created_by = $result['created_by'];
                $this->created_at = $result['created_at'];
                $this->updated_at = $result['updated_at'];
                $this->status = $result['status'];
                return $result;
            }
            return null;
        } catch (Exception $e) {
            throw new Exception('Error reading survey: ' . $e->getMessage());
        }
    }

    // Update survey
    public function update() {
        try {
            $sql = "UPDATE surveys SET title = ?, description = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE survey_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $this->title,
                $this->description,
                $this->status,
                $this->survey_id
            ]);
            
            if ($result) {
                return ['status' => true, 'message' => 'Survey updated successfully'];
            }
            return ['status' => false, 'message' => 'Failed to update survey'];
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error updating survey: ' . $e->getMessage()];
        }
    }

    // Delete survey
    public function delete($survey_id) {
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Delete survey responses first
            $sql1 = "DELETE sr FROM survey_responses sr 
                     INNER JOIN survey_assignments sa ON sr.assignment_id = sa.assignment_id 
                     WHERE sa.survey_id = ?";
            $stmt1 = $this->db->prepare($sql1);
            $stmt1->execute([$survey_id]);
            
            // Delete survey assignments
            $sql2 = "DELETE FROM survey_assignments WHERE survey_id = ?";
            $stmt2 = $this->db->prepare($sql2);
            $stmt2->execute([$survey_id]);
            
            // Delete survey questions
            $sql3 = "DELETE FROM survey_questions WHERE survey_id = ?";
            $stmt3 = $this->db->prepare($sql3);
            $stmt3->execute([$survey_id]);
            
            // Delete survey
            $sql4 = "DELETE FROM surveys WHERE survey_id = ?";
            $stmt4 = $this->db->prepare($sql4);
            $result = $stmt4->execute([$survey_id]);
            
            if ($result) {
                $this->db->commit();
                return ['status' => true, 'message' => 'Survey deleted successfully'];
            } else {
                $this->db->rollBack();
                return ['status' => false, 'message' => 'Failed to delete survey'];
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['status' => false, 'message' => 'Error deleting survey: ' . $e->getMessage()];
        }
    }

    // Get all surveys
    public function getAll() {
        try {
            $sql = "SELECT s.*, u.first_name, u.last_name,
                           (SELECT COUNT(*) FROM survey_questions sq WHERE sq.survey_id = s.survey_id) as question_count,
                           (SELECT COUNT(*) FROM survey_assignments sa WHERE sa.survey_id = s.survey_id) as assignment_count
                    FROM surveys s 
                    LEFT JOIN user u ON s.created_by = u.user_id 
                    ORDER BY s.created_at DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception('Error fetching surveys: ' . $e->getMessage());
        }
    }

    // Get active surveys
    public function getActive() {
        try {
            $sql = "SELECT * FROM surveys WHERE status = 'active' ORDER BY title";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception('Error fetching active surveys: ' . $e->getMessage());
        }
    }
}
