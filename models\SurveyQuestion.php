<?php
require_once __DIR__ . '/../config.php';

class SurveyQuestion {
    private $db;
    private $question_id;
    private $survey_id;
    private $question_text;
    private $question_type;
    private $options;
    private $is_required;
    private $question_order;
    private $created_at;

    public function __construct() {
        $this->db = config::getConnexion();
    }

    // Getters
    public function getQuestionId() { return $this->question_id; }
    public function getSurveyId() { return $this->survey_id; }
    public function getQuestionText() { return $this->question_text; }
    public function getQuestionType() { return $this->question_type; }
    public function getOptions() { return $this->options; }
    public function getIsRequired() { return $this->is_required; }
    public function getQuestionOrder() { return $this->question_order; }
    public function getCreatedAt() { return $this->created_at; }

    // Setters
    public function setQuestionId($question_id) { $this->question_id = $question_id; }
    public function setSurveyId($survey_id) { $this->survey_id = $survey_id; }
    public function setQuestionText($question_text) { $this->question_text = $question_text; }
    public function setQuestionType($question_type) { $this->question_type = $question_type; }
    public function setOptions($options) { $this->options = $options; }
    public function setIsRequired($is_required) { $this->is_required = $is_required; }
    public function setQuestionOrder($question_order) { $this->question_order = $question_order; }
    public function setCreatedAt($created_at) { $this->created_at = $created_at; }

    // Create question
    public function create() {
        try {
            $sql = "INSERT INTO survey_questions (survey_id, question_text, question_type, options, is_required, question_order) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $this->survey_id,
                $this->question_text,
                $this->question_type,
                $this->options,
                $this->is_required ? 1 : 0,
                $this->question_order
            ]);
            
            if ($result) {
                $this->question_id = $this->db->lastInsertId();
                return ['status' => true, 'message' => 'Question created successfully', 'question_id' => $this->question_id];
            }
            return ['status' => false, 'message' => 'Failed to create question'];
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error creating question: ' . $e->getMessage()];
        }
    }

    // Read question by ID
    public function read($question_id) {
        try {
            $sql = "SELECT * FROM survey_questions WHERE question_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$question_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                $this->question_id = $result['question_id'];
                $this->survey_id = $result['survey_id'];
                $this->question_text = $result['question_text'];
                $this->question_type = $result['question_type'];
                $this->options = $result['options'];
                $this->is_required = $result['is_required'];
                $this->question_order = $result['question_order'];
                $this->created_at = $result['created_at'];
                return $result;
            }
            return null;
        } catch (Exception $e) {
            throw new Exception('Error reading question: ' . $e->getMessage());
        }
    }

    // Update question
    public function update() {
        try {
            $sql = "UPDATE survey_questions SET question_text = ?, question_type = ?, options = ?, is_required = ?, question_order = ? 
                    WHERE question_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $this->question_text,
                $this->question_type,
                $this->options,
                $this->is_required ? 1 : 0,
                $this->question_order,
                $this->question_id
            ]);
            
            if ($result) {
                return ['status' => true, 'message' => 'Question updated successfully'];
            }
            return ['status' => false, 'message' => 'Failed to update question'];
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error updating question: ' . $e->getMessage()];
        }
    }

    // Delete question
    public function delete($question_id) {
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Delete responses for this question
            $sql1 = "DELETE FROM survey_responses WHERE question_id = ?";
            $stmt1 = $this->db->prepare($sql1);
            $stmt1->execute([$question_id]);
            
            // Delete question
            $sql2 = "DELETE FROM survey_questions WHERE question_id = ?";
            $stmt2 = $this->db->prepare($sql2);
            $result = $stmt2->execute([$question_id]);
            
            if ($result) {
                $this->db->commit();
                return ['status' => true, 'message' => 'Question deleted successfully'];
            } else {
                $this->db->rollBack();
                return ['status' => false, 'message' => 'Failed to delete question'];
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['status' => false, 'message' => 'Error deleting question: ' . $e->getMessage()];
        }
    }

    // Get questions by survey ID
    public function getBySurveyId($survey_id) {
        try {
            $sql = "SELECT * FROM survey_questions WHERE survey_id = ? ORDER BY question_order, question_id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$survey_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception('Error fetching questions: ' . $e->getMessage());
        }
    }

    // Delete all questions for a survey
    public function deleteBySurveyId($survey_id) {
        try {
            $sql = "DELETE FROM survey_questions WHERE survey_id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$survey_id]);
        } catch (Exception $e) {
            throw new Exception('Error deleting questions: ' . $e->getMessage());
        }
    }
}
