<?php
define('GITHUB_CLIENT_ID', '********************');
define('GITHUB_CLIENT_SECRET', '2f7c121f98b9fdb24c3c03c92bf8686712266430');
define('GITHUB_REDIRECT_URI', 'http://localhost/projects/try3/views/user/front_office/handle_github_auth.php');

// Required GitHub API scopes
define('GITHUB_SCOPES', 'user:email read:user');

// GitHub OAuth endpoints
define('GITHUB_OAUTH_URL', 'https://github.com/login/oauth/authorize');
define('GITHUB_TOKEN_URL', 'https://github.com/login/oauth/access_token');
define('GITHUB_API_URL', 'https://api.github.com');