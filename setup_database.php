<?php
/**
 * Database Setup Script for SurveyMaster Pro
 * Run this file once to create the database and tables
 */

echo "<h2>SurveyMaster Pro - Database Setup</h2>";

// Step 1: Create database if it doesn't exist
try {
    echo "<h3>Step 1: Creating Database</h3>";
    
    // Connect without specifying database
    $pdo = new PDO(
        'mysql:host=localhost',
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `base` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database 'base' created successfully<br>";
    
    // Select the database
    $pdo->exec("USE `base`");
    echo "✅ Connected to database 'base'<br>";
    
} catch (Exception $e) {
    die("❌ Error creating database: " . $e->getMessage());
}

// Step 2: Create user table if it doesn't exist
try {
    echo "<h3>Step 2: Creating User Table</h3>";
    
    $userTableSQL = "
    CREATE TABLE IF NOT EXISTS `user` (
        `user_id` INT AUTO_INCREMENT PRIMARY KEY,
        `first_name` VARCHAR(50) NOT NULL,
        `last_name` VARCHAR(50) NOT NULL,
        `email` VARCHAR(100) UNIQUE NOT NULL,
        `password` VARCHAR(255) NOT NULL,
        `role` ENUM('client', 'agent', 'admin') DEFAULT 'client',
        `verified` TINYINT(1) DEFAULT 0,
        `banned` TINYINT(1) DEFAULT 0,
        `date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `account_type` VARCHAR(20) DEFAULT 'normal',
        `image` VARCHAR(255) NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($userTableSQL);
    echo "✅ User table created successfully<br>";
    
} catch (Exception $e) {
    echo "❌ Error creating user table: " . $e->getMessage() . "<br>";
}

// Step 3: Create survey tables
try {
    echo "<h3>Step 3: Creating Survey Tables</h3>";
    
    // Surveys table
    $surveysSQL = "
    CREATE TABLE IF NOT EXISTS `surveys` (
        `survey_id` INT AUTO_INCREMENT PRIMARY KEY,
        `title` VARCHAR(255) NOT NULL,
        `description` TEXT,
        `created_by` INT NOT NULL,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `status` ENUM('active', 'inactive', 'draft') DEFAULT 'active',
        FOREIGN KEY (`created_by`) REFERENCES `user`(`user_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($surveysSQL);
    echo "✅ Surveys table created successfully<br>";
    
    // Survey questions table
    $questionsSQL = "
    CREATE TABLE IF NOT EXISTS `survey_questions` (
        `question_id` INT AUTO_INCREMENT PRIMARY KEY,
        `survey_id` INT NOT NULL,
        `question_text` TEXT NOT NULL,
        `question_type` ENUM('text', 'multiple_choice', 'rating', 'yes_no') DEFAULT 'text',
        `options` JSON NULL,
        `is_required` BOOLEAN DEFAULT FALSE,
        `question_order` INT DEFAULT 0,
        `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (`survey_id`) REFERENCES `surveys`(`survey_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($questionsSQL);
    echo "✅ Survey questions table created successfully<br>";
    
    // Survey assignments table
    $assignmentsSQL = "
    CREATE TABLE IF NOT EXISTS `survey_assignments` (
        `assignment_id` INT AUTO_INCREMENT PRIMARY KEY,
        `survey_id` INT NOT NULL,
        `client_id` INT NOT NULL,
        `assigned_by` INT NOT NULL,
        `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        `status` ENUM('pending', 'completed', 'expired') DEFAULT 'pending',
        `completed_at` TIMESTAMP NULL,
        FOREIGN KEY (`survey_id`) REFERENCES `surveys`(`survey_id`) ON DELETE CASCADE,
        FOREIGN KEY (`client_id`) REFERENCES `user`(`user_id`) ON DELETE CASCADE,
        FOREIGN KEY (`assigned_by`) REFERENCES `user`(`user_id`) ON DELETE CASCADE,
        UNIQUE KEY `unique_assignment` (`survey_id`, `client_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($assignmentsSQL);
    echo "✅ Survey assignments table created successfully<br>";
    
    // Survey responses table
    $responsesSQL = "
    CREATE TABLE IF NOT EXISTS `survey_responses` (
        `response_id` INT AUTO_INCREMENT PRIMARY KEY,
        `assignment_id` INT NOT NULL,
        `question_id` INT NOT NULL,
        `response_text` TEXT,
        `response_value` INT NULL,
        `submitted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (`assignment_id`) REFERENCES `survey_assignments`(`assignment_id`) ON DELETE CASCADE,
        FOREIGN KEY (`question_id`) REFERENCES `survey_questions`(`question_id`) ON DELETE CASCADE,
        UNIQUE KEY `unique_response` (`assignment_id`, `question_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($responsesSQL);
    echo "✅ Survey responses table created successfully<br>";
    
} catch (Exception $e) {
    echo "❌ Error creating survey tables: " . $e->getMessage() . "<br>";
}

// Step 4: Create default admin user
try {
    echo "<h3>Step 4: Creating Default Admin User</h3>";
    
    // Check if admin exists
    $checkAdmin = $pdo->prepare("SELECT user_id FROM user WHERE email = '<EMAIL>'");
    $checkAdmin->execute();
    
    if (!$checkAdmin->fetch()) {
        $adminSQL = "
        INSERT INTO `user` (first_name, last_name, email, password, role, verified, account_type) 
        VALUES ('Admin', 'User', '<EMAIL>', ?, 'agent', 1, 'normal')
        ";
        
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare($adminSQL);
        $stmt->execute([$hashedPassword]);
        
        echo "✅ Default admin user created<br>";
        echo "📧 Email: <EMAIL><br>";
        echo "🔑 Password: admin123<br>";
    } else {
        echo "✅ Admin user already exists<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error creating admin user: " . $e->getMessage() . "<br>";
}

// Step 5: Insert sample survey data
try {
    echo "<h3>Step 5: Creating Sample Survey Data</h3>";
    
    // Check if sample data exists
    $checkSurvey = $pdo->prepare("SELECT survey_id FROM surveys WHERE title = 'Customer Satisfaction Survey'");
    $checkSurvey->execute();
    
    if (!$checkSurvey->fetch()) {
        // Insert sample survey
        $surveySQL = "INSERT INTO surveys (title, description, created_by, status) VALUES (?, ?, 1, 'active')";
        $stmt = $pdo->prepare($surveySQL);
        $stmt->execute(['Customer Satisfaction Survey', 'Please help us improve our services by answering these questions.']);
        $surveyId = $pdo->lastInsertId();
        
        // Insert sample questions
        $questions = [
            ['How satisfied are you with our service?', 'rating', null, true, 1],
            ['What can we improve?', 'text', null, false, 2],
            ['Would you recommend us to others?', 'yes_no', null, true, 3],
            ['Which of the following best describes your experience?', 'multiple_choice', '["Excellent", "Good", "Average", "Poor", "Very Poor"]', true, 4]
        ];
        
        $questionSQL = "INSERT INTO survey_questions (survey_id, question_text, question_type, options, is_required, question_order) VALUES (?, ?, ?, ?, ?, ?)";
        $questionStmt = $pdo->prepare($questionSQL);
        
        foreach ($questions as $question) {
            $questionStmt->execute([$surveyId, $question[0], $question[1], $question[2], $question[3], $question[4]]);
        }
        
        echo "✅ Sample survey data created<br>";
    } else {
        echo "✅ Sample survey data already exists<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error creating sample data: " . $e->getMessage() . "<br>";
}

echo "<h3>🎉 Database Setup Complete!</h3>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>✅ Database 'base' is ready</li>";
echo "<li>✅ All tables created successfully</li>";
echo "<li>✅ Admin user created (<EMAIL> / admin123)</li>";
echo "<li>✅ Sample survey data added</li>";
echo "</ul>";
echo "<p><a href='views/user/front_office/login.php'>🚀 Go to Login Page</a></p>";

?>
