<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'agent') {
    header('Location: ../front_office/login.php');
    exit();
}
require_once '../../../controllers/UserController.php';
$userController = new UserController();
$users = $userController->getAllUsers();

// Handle survey broadcast form submission
$success = false;
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['survey_id'])) {
    $survey_id = intval($_POST['survey_id']);
    $selected_users = $_POST['selected_users'] ?? [];

    if ($survey_id && !empty($selected_users)) {
        require_once '../../../config.php';
        $pdo = config::getConnexion();

        try {
            $stmt = $pdo->prepare("INSERT INTO survey_assignments (survey_id, client_id, assigned_by) VALUES (?, ?, ?)");
            $assigned_count = 0;
            $skipped_count = 0;

            foreach ($selected_users as $user_id) {
                $user_id = intval($user_id);
                if ($user_id) {
                    // Check if assignment already exists
                    $check_sql = "SELECT assignment_id FROM survey_assignments WHERE survey_id = ? AND client_id = ?";
                    $check_stmt = $pdo->prepare($check_sql);
                    $check_stmt->execute([$survey_id, $user_id]);

                    if (!$check_stmt->fetch()) {
                        $stmt->execute([$survey_id, $user_id, $_SESSION['user_id']]);
                        $assigned_count++;
                    } else {
                        $skipped_count++;
                    }
                }
            }

            if ($assigned_count > 0) {
                $success = true;
                $success_message = "Survey assigned to {$assigned_count} client(s).";
                if ($skipped_count > 0) {
                    $success_message .= " {$skipped_count} client(s) already had this survey assigned.";
                }
            } else {
                $error = 'No new assignments were made. All selected clients already have this survey assigned.';
            }
        } catch (Exception $e) {
            $error = 'Failed to assign survey: ' . $e->getMessage();
            error_log("Error assigning survey: " . $e->getMessage());
        }
    } else {
        $error = 'Please select a survey and at least one client.';
    }
}

// Fetch available surveys
$surveys = [];
try {
    require_once '../../../config.php';
    $pdo = config::getConnexion();
    $surveys_sql = "SELECT survey_id, title, description FROM surveys WHERE status = 'active' ORDER BY title";
    $surveys_stmt = $pdo->prepare($surveys_sql);
    $surveys_stmt->execute();
    $surveys = $surveys_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error fetching surveys: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Survey Broadcast - Hotelia Smart Admin Dashboard</title>
    <meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
    <link rel="icon" href="template/assets/logo/HS.png" type="image/png" />
    <link rel="stylesheet" href="template/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="template/assets/css/plugins.min.css" />
    <link rel="stylesheet" href="template/assets/css/hotelia smart.min.css" />
    <link rel="stylesheet" href="template/assets/css/demo.css" />
</head>
<body>
    <div class="wrapper">
        <?php include 'dashboard_side_bar.php'; ?>
        <div class="main-panel">
            <?php include 'header_bar.php'; ?>
            <div class="content">
                <div class="page-inner">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Broadcast Survey to Multiple Clients</h4>
                                </div>
                                <div class="card-body">
                                    <?php if ($success): ?>
                                        <div class="alert alert-success"><?php echo isset($success_message) ? $success_message : 'Survey broadcast completed!'; ?></div>
                                    <?php elseif ($error): ?>
                                        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                                    <?php endif; ?>

                                    <form method="POST" action="">
                                        <div class="form-group">
                                            <label for="survey_id">Select Survey</label>
                                            <select class="form-control" id="survey_id" name="survey_id" required>
                                                <option value="">Choose a survey to broadcast</option>
                                                <?php foreach ($surveys as $survey): ?>
                                                    <option value="<?php echo $survey['survey_id']; ?>">
                                                        <?php echo htmlspecialchars($survey['title']); ?>
                                                        <?php if ($survey['description']): ?>
                                                            - <?php echo htmlspecialchars(substr($survey['description'], 0, 50)) . (strlen($survey['description']) > 50 ? '...' : ''); ?>
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Select Clients</label>
                                            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="select_all">
                                                    <label class="form-check-label" for="select_all">
                                                        <strong>Select All Clients</strong>
                                                    </label>
                                                </div>
                                                <hr>
                                                <?php foreach ($users as $user): ?>
                                                    <?php if ($user['role'] === 'client'): ?>
                                                        <div class="form-check">
                                                            <input type="checkbox" class="form-check-input client-checkbox"
                                                                   id="user_<?php echo $user['user_id']; ?>"
                                                                   name="selected_users[]"
                                                                   value="<?php echo $user['user_id']; ?>">
                                                            <label class="form-check-label" for="user_<?php echo $user['user_id']; ?>">
                                                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['email'] . ')'); ?>
                                                            </label>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <button type="submit" class="btn btn-primary">Broadcast Survey</button>
                                        <a href="users_management.php" class="btn btn-secondary">Back</a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="template/assets/js/core/jquery-3.7.1.min.js"></script>
    <script src="template/assets/js/core/bootstrap.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('select_all');
            const clientCheckboxes = document.querySelectorAll('.client-checkbox');

            // Select all functionality
            selectAllCheckbox.addEventListener('change', function() {
                clientCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // Update select all checkbox when individual checkboxes change
            clientCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const allChecked = Array.from(clientCheckboxes).every(cb => cb.checked);
                    const noneChecked = Array.from(clientCheckboxes).every(cb => !cb.checked);

                    selectAllCheckbox.checked = allChecked;
                    selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
                });
            });
        });
    </script>
</body>
</html>