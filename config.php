<?php
class config
{
    private static $pdo = null;

    // Database configuration
    private static $host = 'localhost';
    private static $dbname = 'base';
    private static $username = 'root';
    private static $password = '';

    public static function getConnexion()
    {
        if (!isset(self::$pdo)) {
            try {
                $dsn = 'mysql:host=' . self::$host . ';dbname=' . self::$dbname . ';charset=utf8mb4';
                self::$pdo = new PDO(
                    $dsn,
                    self::$username,
                    self::$password,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                    ]
                );
                //echo "connected successfully";
            } catch (PDOException $e) {
                // Better error handling with helpful messages
                $errorMsg = "Database connection failed. Please check:\n";
                $errorMsg .= "1. XAMPP MySQL service is running\n";
                $errorMsg .= "2. Database 'base' exists\n";
                $errorMsg .= "3. Connection parameters are correct\n\n";
                $errorMsg .= "Technical error: " . $e->getMessage();

                // In development, show detailed error
                if (self::isDevelopment()) {
                    die('<div style="background:#f8d7da;color:#721c24;padding:20px;border:1px solid #f5c6cb;border-radius:5px;margin:20px;font-family:Arial,sans-serif;">
                        <h3>🚨 Database Connection Error</h3>
                        <p><strong>Please follow these steps:</strong></p>
                        <ol>
                            <li>Make sure XAMPP is running (Apache + MySQL)</li>
                            <li>Run the database setup: <a href="setup_database.php" style="color:#721c24;"><strong>setup_database.php</strong></a></li>
                            <li>Check that database "base" exists in phpMyAdmin</li>
                        </ol>
                        <details>
                            <summary>Technical Details</summary>
                            <pre>' . htmlspecialchars($errorMsg) . '</pre>
                        </details>
                    </div>');
                } else {
                    // In production, show generic error
                    die('Database connection error. Please contact support.');
                }
            }
        }
        return self::$pdo;
    }

    // Check if we're in development environment
    private static function isDevelopment()
    {
        return in_array($_SERVER['SERVER_NAME'] ?? '', ['localhost', '127.0.0.1', '::1']) ||
               strpos($_SERVER['SERVER_NAME'] ?? '', '.local') !== false;
    }

    // Test database connection
    public static function testConnection()
    {
        try {
            $pdo = self::getConnexion();
            $stmt = $pdo->query("SELECT 1");
            return ['status' => true, 'message' => 'Database connection successful'];
        } catch (Exception $e) {
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }
}

// Expose a global $conn variable for legacy MySQLi-style code
try {
    $conn = config::getConnexion();
} catch (Exception $e) {
    // Handle connection error gracefully
    $conn = null;
}
?>
