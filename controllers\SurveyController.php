<?php
require_once __DIR__ . '/../models/Survey.php';
require_once __DIR__ . '/../models/SurveyQuestion.php';

class SurveyController {
    private $surveyModel;
    private $questionModel;

    public function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->surveyModel = new Survey();
        $this->questionModel = new SurveyQuestion();
    }

    // Check if user is authorized (agent role)
    private function checkAuthorization() {
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'agent') {
            return false;
        }
        return true;
    }

    // Create survey
    public function createSurvey($title, $description, $status = 'active') {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        if (empty($title)) {
            return ['status' => false, 'message' => 'Survey title is required'];
        }

        $this->surveyModel->setTitle($title);
        $this->surveyModel->setDescription($description);
        $this->surveyModel->setCreatedBy($_SESSION['user_id']);
        $this->surveyModel->setStatus($status);

        return $this->surveyModel->create();
    }

    // Get survey by ID
    public function getSurvey($survey_id) {
        if (!$this->checkAuthorization()) {
            return null;
        }

        try {
            return $this->surveyModel->read($survey_id);
        } catch (Exception $e) {
            return null;
        }
    }

    // Update survey
    public function updateSurvey($survey_id, $title, $description, $status) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        if (empty($title)) {
            return ['status' => false, 'message' => 'Survey title is required'];
        }

        $this->surveyModel->setSurveyId($survey_id);
        $this->surveyModel->setTitle($title);
        $this->surveyModel->setDescription($description);
        $this->surveyModel->setStatus($status);

        return $this->surveyModel->update();
    }

    // Delete survey
    public function deleteSurvey($survey_id) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        return $this->surveyModel->delete($survey_id);
    }

    // Get all surveys
    public function getAllSurveys() {
        if (!$this->checkAuthorization()) {
            return [];
        }

        try {
            return $this->surveyModel->getAll();
        } catch (Exception $e) {
            return [];
        }
    }

    // Get active surveys
    public function getActiveSurveys() {
        try {
            return $this->surveyModel->getActive();
        } catch (Exception $e) {
            return [];
        }
    }

    // Create question
    public function createQuestion($survey_id, $question_text, $question_type, $options = null, $is_required = false, $question_order = 0) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        if (empty($question_text)) {
            return ['status' => false, 'message' => 'Question text is required'];
        }

        $this->questionModel->setSurveyId($survey_id);
        $this->questionModel->setQuestionText($question_text);
        $this->questionModel->setQuestionType($question_type);
        $this->questionModel->setOptions($options);
        $this->questionModel->setIsRequired($is_required);
        $this->questionModel->setQuestionOrder($question_order);

        return $this->questionModel->create();
    }

    // Get question by ID
    public function getQuestion($question_id) {
        if (!$this->checkAuthorization()) {
            return null;
        }

        try {
            return $this->questionModel->read($question_id);
        } catch (Exception $e) {
            return null;
        }
    }

    // Update question
    public function updateQuestion($question_id, $question_text, $question_type, $options = null, $is_required = false, $question_order = 0) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        if (empty($question_text)) {
            return ['status' => false, 'message' => 'Question text is required'];
        }

        $this->questionModel->setQuestionId($question_id);
        $this->questionModel->setQuestionText($question_text);
        $this->questionModel->setQuestionType($question_type);
        $this->questionModel->setOptions($options);
        $this->questionModel->setIsRequired($is_required);
        $this->questionModel->setQuestionOrder($question_order);

        return $this->questionModel->update();
    }

    // Delete question
    public function deleteQuestion($question_id) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        return $this->questionModel->delete($question_id);
    }

    // Get questions by survey ID
    public function getQuestionsBySurvey($survey_id) {
        if (!$this->checkAuthorization()) {
            return [];
        }

        try {
            return $this->questionModel->getBySurveyId($survey_id);
        } catch (Exception $e) {
            return [];
        }
    }

    // Process survey form submission
    public function processSurveyForm($data) {
        if (!$this->checkAuthorization()) {
            return ['status' => false, 'message' => 'Unauthorized access'];
        }

        try {
            if (isset($data['survey_id']) && !empty($data['survey_id'])) {
                // Update existing survey
                $result = $this->updateSurvey(
                    $data['survey_id'],
                    $data['title'],
                    $data['description'],
                    $data['status']
                );
            } else {
                // Create new survey
                $result = $this->createSurvey(
                    $data['title'],
                    $data['description'],
                    $data['status']
                );
            }

            if ($result['status'] && isset($data['questions'])) {
                $survey_id = $result['survey_id'] ?? $data['survey_id'];
                
                // Delete existing questions if updating
                if (isset($data['survey_id']) && !empty($data['survey_id'])) {
                    try {
                        $this->questionModel->deleteBySurveyId($survey_id);
                    } catch (Exception $e) {
                        // Log error but continue
                        error_log("Error deleting existing questions: " . $e->getMessage());
                    }
                }

                // Add new questions
                foreach ($data['questions'] as $index => $question) {
                    if (!empty($question['text'])) {
                        $this->createQuestion(
                            $survey_id,
                            $question['text'],
                            $question['type'],
                            isset($question['options']) ? json_encode($question['options']) : null,
                            isset($question['required']) ? true : false,
                            $index + 1
                        );
                    }
                }
            }

            return $result;
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Error processing survey: ' . $e->getMessage()];
        }
    }
}
